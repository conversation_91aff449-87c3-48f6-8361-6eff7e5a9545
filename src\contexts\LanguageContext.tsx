
import React, { createContext, useContext, useState } from 'react';

export type Language = 'en' | 'hi' | 'ur';

interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string) => string;
}

const translations = {
  en: {
    // Header
    home: "Home",
    services: "Services", 
    about: "About",
    contact: "Contact",
    
    // Hero Section
    heroTitle: "Welcome to Graphic and Cyber Support",
    heroSubtitle: "Your Trusted Cyber & Printing Partner",
    heroDescription: "We are your local printing, scanning, online form filling, and documentation needs one-stop solution. Fast service, affordable rates, and customer satisfaction are our priority.",
    callNow: "Call Now",
    visitShop: "Visit Our Shop",
    fillForm: "Fill Online Form",
    
    // Stats
    happyCustomers: "Happy Customers",
    formsFilled: "Forms Filled",
    experience: "Experience",
    
    // Services
    servicesTitle: "Our Services",
    servicesSubtitle: "Complete solution for all your cyber and printing needs",
    premiumServices: "Premium Services",
    popular: "POPULAR",
    specialOffer: "✨ Special Offer ✨",
    specialOfferDesc: "Get FREE scanning service with 10+ forms! 20% discount on bulk printing!",
    
    onlineFormFilling: "Online Form Filling",
    onlineFormFillingDesc: "Govt. Forms, College Admission, Job Applications",
    onlineFormFillingPrice: "Starting ₹50",
    
    documentPrinting: "Document Printing",
    documentPrintingDesc: "Color & B&W Printing, Resume, Cover Letters",
    documentPrintingPrice: "₹2-10 per page",
    
    photocopyScanning: "Photocopy & Scanning",
    photocopyScanningDesc: "High Quality Copying & Digital Scanning", 
    photocopyScanningPrice: "₹1-5 per page",
    
    resumeCreation: "Resume & CV Creation",
    resumeCreationDesc: "Professional Resume & Cover Letter Design",
    resumeCreationPrice: "₹100-300",
    
    panAadhar: "PAN Card / Aadhar Help",
    panAadharDesc: "Apply, Update, Correction Services",
    panAadharPrice: "₹100-200",
    
    emailPayment: "Email & Payment Assistance",
    emailPaymentDesc: "Email Setup, Online Payment Help",
    emailPaymentPrice: "₹50-100",
    
    passportPhoto: "Passport Photo Printing",
    passportPhotoDesc: "All Size Photos, ID Photos",
    passportPhotoPrice: "₹20-50",
    
    digitalDoc: "Digital Documentation",
    digitalDocDesc: "Document Digitization & Storage",
    digitalDocPrice: "₹30-100",
    
    // About
    aboutTitle: "About Graphic and Cyber Support",
    aboutSubtitle: "Your Local Technology Partner Since 2019",
    ourStory: "About Me",
    aboutDescription1: "Graphic and Cyber Support is a local cyber service provider serving you since 2019. Our aim is to provide fast, reliable, and quality service to every customer.",
    aboutDescription2: "Whether you need a small print or online form filling - we are here with you! Our team is experienced and uses the latest technology.",
    aboutDescription3: "We believe in 'Customer First' approach and that's why our customers choose us again and again.",
    
    qualityService: "Quality Service",
    qualityServiceDesc: "Top quality printing and accurate form filling",
    
    fastService: "Fast Service", 
    fastServiceDesc: "Quick turnaround time with guaranteed delivery",
    
    expertTeam: "Expert Team",
    expertTeamDesc: "Experienced professionals who help you",
    
    customerSatisfaction: "Customer Satisfaction",
    customerSatisfactionDesc: "500+ happy customers and growing!",
    
    testimonialText: "Excellent service! Got my college admission form filled here, it was completely accurate and completed on time. Highly recommended!",
    testimonialAuthor: "- Priya Sharma, Student",
    
    // Contact
    contactTitle: "Get in Touch",
    contactSubtitle: "Visit us or call for any assistance",
    ourAddress: "Our Address",
    addressLine1: "Shop No. 15, Main Market Road,",
    addressLine2: "Near Bus Stand, City Center",
    addressLine3: "PIN: 123456",
    addressLandmark: "(Opposite State Bank of India)",
    
    phoneWhatsapp: "Phone & WhatsApp",
    callLabel: "📞 Call:",
    whatsappLabel: "📱 WhatsApp:",
    whatsappNow: "WhatsApp Now",
    
    shopTimings: "Shop Timings",
    weekdays: "Monday - Saturday:",
    weekdayHours: "10:00 AM - 7:00 PM",
    sunday: "Sunday:",
    sundayHours: "11:00 AM - 5:00 PM",
    emergencyServices: "✅ Emergency services available on call",
    
    emailUs: "Email Us",
    emailResponse: "We reply within 24 hours",
    
    findUsOnMap: "Find Us on Map",
    interactiveMap: "Interactive Map",
    clickDirections: "Click for directions",
    openInGoogleMaps: "Open in Google Maps",
    
    quickEnquiry: "Quick Enquiry",
    yourName: "Your Name",
    phoneNumber: "Phone Number", 
    whatServiceNeed: "What service do you need?",
    sendEnquiry: "Send Enquiry",
    
    importantNotice: "🔔 Important Notice",
    covidSafety: "COVID safety protocols followed. Mask required inside shop.",
    digitalPayment: "Digital payment preferred - UPI, Card accepted.",
    
    // Footer
    footerTagline: "Your Trusted Local Cyber & Printing Partner"
  },
  hi: {
    // Header
    home: "होम",
    services: "सेवाएं",
    about: "हमारे बारे में",
    contact: "संपर्क",
    
    // Hero Section
    heroTitle: "ग्राफिक एंड साइबर सपोर्ट में आपका स्वागत है",
    heroSubtitle: "आपका भरोसेमंद साइबर और प्रिंटिंग पार्टनर",
    heroDescription: "हम आपके स्थानीय प्रिंटिंग, स्कैनिंग, ऑनलाइन फॉर्म भरने और दस्तावेज़ीकरण की जरूरतों का वन-स्टॉप समाधान हैं। तेज़ सेवा, किफायती दरें और ग्राहक संतुष्टि हमारी प्राथमिकता है।",
    callNow: "अभी कॉल करें",
    visitShop: "हमारी दुकान पर आएं",
    fillForm: "ऑनलाइन फॉर्म भरें",
    
    // Stats
    happyCustomers: "खुश ग्राहक",
    formsFilled: "भरे गए फॉर्म",
    experience: "अनुभव",
    
    // Services
    servicesTitle: "हमारी सेवाएं",
    servicesSubtitle: "आपकी सभी साइबर और प्रिंटिंग जरूरतों का पूरा समाधान",
    premiumServices: "प्रीमियम सेवाएं",
    popular: "लोकप्रिय",
    specialOffer: "✨ विशेष ऑफर ✨",
    specialOfferDesc: "10+ फॉर्म के साथ मुफ्त स्कैनिंग सेवा! बल्क प्रिंटिंग पर 20% छूट!",
    
    onlineFormFilling: "ऑनलाइन फॉर्म भरना",
    onlineFormFillingDesc: "सरकारी फॉर्म, कॉलेज एडमिशन, नौकरी आवेदन",
    onlineFormFillingPrice: "₹50 से शुरू",
    
    documentPrinting: "दस्तावेज़ प्रिंटिंग",
    documentPrintingDesc: "रंगीन और ब्लैक-व्हाइट प्रिंटिंग, रिज्यूमे, कवर लेटर",
    documentPrintingPrice: "₹2-10 प्रति पेज",
    
    photocopyScanning: "फोटोकॉपी और स्कैनिंग",
    photocopyScanningDesc: "उच्च गुणवत्ता कॉपी और डिजिटल स्कैनिंग",
    photocopyScanningPrice: "₹1-5 प्रति पेज",
    
    resumeCreation: "रिज्यूमे और सीवी बनाना",
    resumeCreationDesc: "व्यावसायिक रिज्यूमे और कवर लेटर डिज़ाइन",
    resumeCreationPrice: "₹100-300",
    
    panAadhar: "पैन कार्ड / आधार सहायता",
    panAadharDesc: "आवेदन, अपडेट, सुधार सेवाएं",
    panAadharPrice: "₹100-200",
    
    emailPayment: "ईमेल और पेमेंट सहायता",
    emailPaymentDesc: "ईमेल सेटअप, ऑनलाइन पेमेंट सहायता",
    emailPaymentPrice: "₹50-100",
    
    passportPhoto: "पासपोर्ट फोटो प्रिंटिंग",
    passportPhotoDesc: "सभी साइज़ फोटो, आईडी फोटो",
    passportPhotoPrice: "₹20-50",
    
    digitalDoc: "डिजिटल दस्तावेज़ीकरण",
    digitalDocDesc: "दस्तावेज़ डिजिटाइज़ेशन और स्टोरेज",
    digitalDocPrice: "₹30-100",
    
    // About
    aboutTitle: "ग्राफिक एंड साइबर सपोर्ट के बारे में",
    aboutSubtitle: "2019 से आपका स्थानीय तकनीकी पार्टनर",
    ourStory: "हमारी कहानी",
    aboutDescription1: "ग्राफिक एंड साइबर सपोर्ट एक स्थानीय साइबर सेवा प्रदाता है जो 2019 से आपकी सेवा में है। हमारा उद्देश्य हर ग्राहक को तेज़, विश्वसनीय और गुणवत्तापूर्ण सेवा प्रदान करना है।",
    aboutDescription2: "चाहे आपको एक छोटी सी प्रिंट चाहिए हो या ऑनलाइन फॉर्म भरना हो - हम आपके साथ हैं! हमारी टीम अनुभवी है और नवीनतम तकनीक का उपयोग करती है।",
    aboutDescription3: "हम 'ग्राहक पहले' के दृष्टिकोण में विश्वास करते हैं और इसीलिए हमारे ग्राहक हमें बार-बार चुनते हैं।",
    
    qualityService: "गुणवत्तापूर्ण सेवा",
    qualityServiceDesc: "उच्च गुणवत्ता प्रिंटिंग और सटीक फॉर्म भरना",
    
    fastService: "तेज़ सेवा",
    fastServiceDesc: "गारंटीशुदा डिलीवरी के साथ त्वरित समय",
    
    expertTeam: "विशेषज्ञ टीम",
    expertTeamDesc: "अनुभवी पेशेवर जो आपकी मदद करते हैं",
    
    customerSatisfaction: "ग्राहक संतुष्टि",
    customerSatisfactionDesc: "500+ खुश ग्राहक और बढ़ रहे हैं!",
    
    testimonialText: "बहुत अच्छी सेवा! मेरा कॉलेज एडमिशन फॉर्म यहाँ भरवाया था, बिलकुल सटीक था और समय पर पूरा हो गया। अत्यधिक सिफारिशीय!",
    testimonialAuthor: "- प्रिया शर्मा, छात्रा",
    
    // Contact
    contactTitle: "संपर्क में रहें",
    contactSubtitle: "हमसे मिलें या किसी भी सहायता के लिए कॉल करें",
    ourAddress: "हमारा पता",
    addressLine1: "दुकान नं. 15, मुख्य बाज़ार रोड,",
    addressLine2: "बस स्टैंड के पास, सिटी सेंटर",
    addressLine3: "पिन: 123456",
    addressLandmark: "(स्टेट बैंक ऑफ इंडिया के सामने)",
    
    phoneWhatsapp: "फोन और व्हाट्सऐप",
    callLabel: "📞 कॉल:",
    whatsappLabel: "📱 व्हाट्सऐप:",
    whatsappNow: "अभी व्हाट्सऐप करें",
    
    shopTimings: "दुकान का समय",
    weekdays: "सोमवार - शनिवार:",
    weekdayHours: "सुबह 10:00 - शाम 7:00",
    sunday: "रविवार:",
    sundayHours: "सुबह 11:00 - शाम 5:00",
    emergencyServices: "✅ आपातकालीन सेवाएं कॉल पर उपलब्ध",
    
    emailUs: "हमें ईमेल करें",
    emailResponse: "हम 24 घंटे में जवाब देते हैं",
    
    findUsOnMap: "मैप पर हमें खोजें",
    interactiveMap: "इंटरैक्टिव मैप",
    clickDirections: "दिशाओं के लिए क्लिक करें",
    openInGoogleMaps: "Google मैप में खोलें",
    
    quickEnquiry: "त्वरित पूछताछ",
    yourName: "आपका नाम",
    phoneNumber: "फोन नंबर",
    whatServiceNeed: "आपको कौन सी सेवा चाहिए?",
    sendEnquiry: "पूछताछ भेजें",
    
    importantNotice: "🔔 महत्वपूर्ण सूचना",
    covidSafety: "COVID सुरक्षा प्रोटोकॉल का पालन। दुकान के अंदर मास्क आवश्यक।",
    digitalPayment: "डिजिटल पेमेंट प्राथमिकता - UPI, कार्ड स्वीकार्य।",
    
    // Footer
    footerTagline: "आपका भरोसेमंद स्थानीय साइबर और प्रिंटिंग पार्टनर"
  },
  ur: {
    // Header
    home: "ہوم",
    services: "خدمات",
    about: "ہمارے بارے میں",
    contact: "رابطہ",
    
    // Hero Section
    heroTitle: "گرافک اینڈ سائبر سپورٹ میں خوش آمدید",
    heroSubtitle: "آپ کا قابل اعتماد سائبر اور پرنٹنگ پارٹنر",
    heroDescription: "ہم آپ کی مقامی پرنٹنگ، اسکیننگ، آن لائن فارم بھرنے اور دستاویزات کی ضروریات کا ون اسٹاپ حل ہیں۔ تیز سروس، سستی قیمتیں اور کسٹمر کی اطمینان ہماری ترجیح ہے۔",
    callNow: "ابھی کال کریں",
    visitShop: "ہماری دکان آئیں",
    fillForm: "آن لائن فارم بھریں",
    
    // Stats
    happyCustomers: "خوش کسٹمرز",
    formsFilled: "بھرے گئے فارمز",
    experience: "تجربہ",
    
    // Services
    servicesTitle: "ہماری خدمات",
    servicesSubtitle: "آپ کی تمام سائبر اور پرنٹنگ ضروریات کا مکمل حل",
    premiumServices: "پریمیم خدمات",
    popular: "مقبول",
    specialOffer: "✨ خصوصی پیشکش ✨",
    specialOfferDesc: "10+ فارمز کے ساتھ مفت اسکیننگ سروس! بلک پرنٹنگ پر 20% رعایت!",
    
    onlineFormFilling: "آن لائن فارم بھرنا",
    onlineFormFillingDesc: "حکومتی فارمز، کالج داخلہ، ملازمت کی درخواست",
    onlineFormFillingPrice: "₹50 سے شروع",
    
    documentPrinting: "دستاویز پرنٹنگ",
    documentPrintingDesc: "رنگین اور سیاہ سفید پرنٹنگ، ریزیومے، کور لیٹر",
    documentPrintingPrice: "₹2-10 فی صفحہ",
    
    photocopyScanning: "فوٹو کاپی اور اسکیننگ",
    photocopyScanningDesc: "اعلیٰ معیار کی کاپی اور ڈیجیٹل اسکیننگ",
    photocopyScanningPrice: "₹1-5 فی صفحہ",
    
    resumeCreation: "ریزیومے اور سی وی بنانا",
    resumeCreationDesc: "پیشہ ورانہ ریزیومے اور کور لیٹر ڈیزائن",
    resumeCreationPrice: "₹100-300",
    
    panAadhar: "پین کارڈ / آدھار مدد",
    panAadharDesc: "درخواست، اپ ڈیٹ، اصلاح کی خدمات",
    panAadharPrice: "₹100-200",
    
    emailPayment: "ای میل اور ادائیگی مدد",
    emailPaymentDesc: "ای میل سیٹ اپ، آن لائن ادائیگی مدد",
    emailPaymentPrice: "₹50-100",
    
    passportPhoto: "پاسپورٹ فوٹو پرنٹنگ",
    passportPhotoDesc: "تمام سائز کے فوٹو، شناختی فوٹو",
    passportPhotoPrice: "₹20-50",
    
    digitalDoc: "ڈیجیٹل دستاویزات",
    digitalDocDesc: "دستاویز ڈیجیٹائزیشن اور اسٹوریج",
    digitalDocPrice: "₹30-100",
    
    // About
    aboutTitle: "گرافک اینڈ سائبر سپورٹ کے بارے میں",
    aboutSubtitle: "2019 سے آپ کا مقامی ٹیکنالوجی پارٹنر",
    ourStory: "ہماری کہانی",
    aboutDescription1: "گرافک اینڈ سائبر سپورٹ ایک مقامی سائبر خدمات فراہم کنندہ ہے جو 2019 سے آپ کی خدمت میں ہے۔ ہمارا مقصد ہر کسٹمر کو تیز، قابل اعتماد اور معیاری خدمات فراہم کرنا ہے۔",
    aboutDescription2: "چاہے آپ کو ایک چھوٹی سی پرنٹ چاہیے یا آن لائن فارم بھرنا ہو - ہم آپ کے ساتھ ہیں! ہماری ٹیم تجربہ کار ہے اور جدید ٹیکنالوجی استعمال کرتی ہے۔",
    aboutDescription3: "ہم 'کسٹمر پہلے' کے نقطہ نظر میں یقین رکھتے ہیں اور اسی لیے ہمارے کسٹمرز ہمیں بار بار منتخب کرتے ہیں۔",
    
    qualityService: "معیاری سروس",
    qualityServiceDesc: "اعلیٰ معیار کی پرنٹنگ اور درست فارم بھرنا",
    
    fastService: "تیز سروس",
    fastServiceDesc: "گارنٹیڈ ڈیلیوری کے ساتھ فوری وقت",
    
    expertTeam: "ماہر ٹیم",
    expertTeamDesc: "تجربہ کار پیشہ ور جو آپ کی مدد کرتے ہیں",
    
    customerSatisfaction: "کسٹمر کی اطمینان",
    customerSatisfactionDesc: "500+ خوش کسٹمرز اور بڑھ رہے ہیں!",
    
    testimonialText: "بہترین سروس! میرا کالج داخلہ فارم یہاں بھرایا تھا، بالکل درست تھا اور وقت پر مکمل ہوا۔ انتہائی تجویز کردہ!",
    testimonialAuthor: "- پریا شرما، طالبہ",
    
    // Contact
    contactTitle: "رابطے میں رہیں",
    contactSubtitle: "ہم سے ملیں یا کسی بھی مدد کے لیے کال کریں",
    ourAddress: "ہمارا پتہ",
    addressLine1: "دکان نمبر 15، مین مارکیٹ روڈ،",
    addressLine2: "بس سٹینڈ کے پاس، سٹی سینٹر",
    addressLine3: "پن: 123456",
    addressLandmark: "(اسٹیٹ بینک آف انڈیا کے سامنے)",
    
    phoneWhatsapp: "فون اور واٹس ایپ",
    callLabel: "📞 کال:",
    whatsappLabel: "📱 واٹس ایپ:",
    whatsappNow: "ابھی واٹس ایپ کریں",
    
    shopTimings: "دکان کا وقت",
    weekdays: "پیر - ہفتہ:",
    weekdayHours: "صبح 10:00 - شام 7:00",
    sunday: "اتوار:",
    sundayHours: "صبح 11:00 - شام 5:00",
    emergencyServices: "✅ ہنگامی خدمات کال پر دستیاب",
    
    emailUs: "ہمیں ای میل کریں",
    emailResponse: "ہم 24 گھنٹے میں جواب دیتے ہیں",
    
    findUsOnMap: "نقشے پر ہمیں تلاش کریں",
    interactiveMap: "انٹرایکٹو نقشہ",
    clickDirections: "سمت کے لیے کلک کریں",
    openInGoogleMaps: "Google Maps میں کھولیں",
    
    quickEnquiry: "فوری استفسار",
    yourName: "آپ کا نام",
    phoneNumber: "فون نمبر",
    whatServiceNeed: "آپ کو کیا سروس چاہیے؟",
    sendEnquiry: "استفسار بھیجیں",
    
    importantNotice: "🔔 اہم اطلاع",
    covidSafety: "COVID حفاظتی پروٹوکول کی پیروی۔ دکان کے اندر ماسک ضروری۔",
    digitalPayment: "ڈیجیٹل ادائیگی ترجیحی - UPI، کارڈ قبول۔",
    
    // Footer
    footerTagline: "آپ کا قابل اعتماد مقامی سائبر اور پرنٹنگ پارٹنر"
  }
};

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export const LanguageProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [language, setLanguage] = useState<Language>('en');

  const t = (key: string): string => {
    return translations[language][key as keyof typeof translations[typeof language]] || key;
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};
