import React from 'react';
import { Search, Globe, MapPin, Star, CheckCircle, ExternalLink } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

const SEOAnalysis = () => {
  const seoFeatures = [
    {
      category: "Primary Meta Tags",
      icon: <Search className="h-5 w-5" />,
      items: [
        "✅ Optimized title tag (60 characters)",
        "✅ Meta description (155 characters)",
        "✅ Keywords meta tag with local terms",
        "✅ Author and robots meta tags",
        "✅ Language and revisit-after tags"
      ]
    },
    {
      category: "Local SEO",
      icon: <MapPin className="h-5 w-5" />,
      items: [
        "✅ Geo meta tags with coordinates",
        "✅ Business contact information",
        "✅ Local business structured data",
        "✅ Google Maps integration",
        "✅ Address and phone number markup"
      ]
    },
    {
      category: "Social Media",
      icon: <Globe className="h-5 w-5" />,
      items: [
        "✅ Open Graph meta tags",
        "✅ Twitter Card meta tags",
        "✅ Facebook business markup",
        "✅ Social sharing optimization",
        "✅ Image meta tags for sharing"
      ]
    },
    {
      category: "Technical SEO",
      icon: <Star className="h-5 w-5" />,
      items: [
        "✅ Canonical URL specified",
        "✅ Structured data (JSON-LD)",
        "✅ Sitemap.xml created",
        "✅ Robots.txt configured",
        "✅ PWA manifest.json"
      ]
    }
  ];

  const structuredData = {
    "@type": "LocalBusiness",
    "name": "Graphic & Cyber Support",
    "address": "Kolkata, West Bengal, India",
    "phone": "+91-74494-23849",
    "coordinates": "22.8030907, 88.3462475",
    "services": ["Printing", "Scanning", "Documentation", "Cyber Services"]
  };

  const seoTools = [
    {
      name: "Google Search Console",
      url: "https://search.google.com/search-console",
      description: "Monitor search performance"
    },
    {
      name: "Google My Business",
      url: "https://business.google.com/",
      description: "Manage local business listing"
    },
    {
      name: "PageSpeed Insights",
      url: "https://pagespeed.web.dev/",
      description: "Check website performance"
    },
    {
      name: "Rich Results Test",
      url: "https://search.google.com/test/rich-results",
      description: "Test structured data"
    }
  ];

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-800 mb-2">SEO Optimization Report</h1>
        <p className="text-gray-600">Complete SEO implementation for Graphic & Cyber Support</p>
      </div>

      {/* SEO Features Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {seoFeatures.map((feature, index) => (
          <Card key={index}>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                {feature.icon}
                <span>{feature.category}</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2">
                {feature.items.map((item, i) => (
                  <li key={i} className="text-sm flex items-start space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                    <span>{item}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Structured Data Preview */}
      <Card>
        <CardHeader>
          <CardTitle>Structured Data (JSON-LD)</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-gray-50 p-4 rounded-lg">
            <pre className="text-sm overflow-x-auto">
              <code>{JSON.stringify(structuredData, null, 2)}</code>
            </pre>
          </div>
          <p className="text-sm text-gray-600 mt-2">
            This structured data helps search engines understand your business information.
          </p>
        </CardContent>
      </Card>

      {/* Key SEO Metrics */}
      <Card>
        <CardHeader>
          <CardTitle>Key SEO Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-gray-800 mb-3">Primary Keywords:</h4>
              <div className="flex flex-wrap gap-2">
                {[
                  "printing services Kolkata",
                  "cyber cafe",
                  "document scanning",
                  "photocopying",
                  "digital printing",
                  "online forms",
                  "documentation services",
                  "West Bengal"
                ].map((keyword, i) => (
                  <span key={i} className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">
                    {keyword}
                  </span>
                ))}
              </div>
            </div>
            
            <div>
              <h4 className="font-medium text-gray-800 mb-3">Business Information:</h4>
              <ul className="text-sm space-y-1">
                <li><strong>Name:</strong> Graphic & Cyber Support</li>
                <li><strong>Location:</strong> Kolkata, West Bengal</li>
                <li><strong>Phone:</strong> +91-74494-23849</li>
                <li><strong>Coordinates:</strong> 22.8030907, 88.3462475</li>
                <li><strong>Hours:</strong> Mo-Su 10:00-19:00</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* SEO Tools */}
      <Card>
        <CardHeader>
          <CardTitle>Recommended SEO Tools</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {seoTools.map((tool, index) => (
              <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <h4 className="font-medium">{tool.name}</h4>
                  <p className="text-sm text-gray-600">{tool.description}</p>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.open(tool.url, '_blank')}
                >
                  <ExternalLink className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Next Steps */}
      <Card>
        <CardHeader>
          <CardTitle>Next Steps for SEO</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h4 className="font-medium text-green-600 mb-2">✅ Completed:</h4>
              <ul className="text-sm space-y-1 text-gray-600">
                <li>• All meta tags optimized</li>
                <li>• Structured data implemented</li>
                <li>• Local SEO setup complete</li>
                <li>• Social media optimization done</li>
                <li>• Technical SEO foundations set</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium text-blue-600 mb-2">📋 Recommended Actions:</h4>
              <ul className="text-sm space-y-1 text-gray-600">
                <li>• Submit sitemap to Google Search Console</li>
                <li>• Create Google My Business listing</li>
                <li>• Add business to local directories</li>
                <li>• Create quality content about services</li>
                <li>• Encourage customer reviews</li>
                <li>• Monitor performance with analytics</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SEOAnalysis;
