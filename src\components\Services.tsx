
import { FileText, Printer, Copy, User, CreditCard, Mail, Camera, CheckCircle, Sparkles } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { useLanguage } from "@/contexts/LanguageContext";

const Services = () => {
  const { t } = useLanguage();
  
  const services = [
    {
      icon: FileText,
      title: t('onlineFormFilling'),
      description: t('onlineFormFillingDesc'),
      price: t('onlineFormFillingPrice'),
      color: "blue",
      popular: false
    },
    {
      icon: Printer,
      title: t('documentPrinting'),
      description: t('documentPrintingDesc'),
      price: t('documentPrintingPrice'),
      color: "green",
      popular: true
    },
    {
      icon: Copy,
      title: t('photocopyScanning'),
      description: t('photocopyScanningDesc'),
      price: t('photocopyScanningPrice'),
      color: "purple",
      popular: false
    },
    {
      icon: User,
      title: t('resumeCreation'),
      description: t('resumeCreationDesc'),
      price: t('resumeCreationPrice'),
      color: "orange",
      popular: false
    },
    {
      icon: CreditCard,
      title: t('panAadhar'),
      description: t('panAadharDesc'),
      price: t('panAadharPrice'),
      color: "red",
      popular: true
    },
    {
      icon: Mail,
      title: t('emailPayment'),
      description: t('emailPaymentDesc'),
      price: t('emailPaymentPrice'),
      color: "indigo",
      popular: false
    },
    {
      icon: Camera,
      title: t('passportPhoto'),
      description: t('passportPhotoDesc'),
      price: t('passportPhotoPrice'),
      color: "pink",
      popular: false
    },
    {
      icon: CheckCircle,
      title: t('digitalDoc'),
      description: t('digitalDocDesc'),
      price: t('digitalDocPrice'),
      color: "teal",
      popular: false
    }
  ];

  return (
    <section id="services" className="py-20 px-4 bg-gradient-to-br from-white via-blue-50/30 to-purple-50/30 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 right-10 w-32 h-32 bg-gradient-to-br from-blue-400/10 to-purple-400/10 rounded-full blur-2xl animate-pulse delay-1000"></div>
        <div className="absolute bottom-20 left-10 w-40 h-40 bg-gradient-to-br from-green-400/10 to-blue-400/10 rounded-full blur-2xl animate-pulse delay-2000"></div>
      </div>

      <div className="container mx-auto relative z-10">
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-white/80 backdrop-blur-md rounded-full px-6 py-3 mb-6 shadow-lg border border-white/20 animate-fade-in">
            <Sparkles className="h-5 w-5 text-purple-500 animate-spin" />
            <span className="text-sm font-medium text-purple-600">{t('premiumServices')}</span>
          </div>
          
          <h2 className="text-4xl md:text-6xl font-bold mb-4 animate-fade-in delay-200">
            <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-blue-600 bg-clip-text text-transparent">
              {t('servicesTitle')}
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto animate-fade-in delay-300">
            {t('servicesSubtitle')}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {services.map((service, index) => (
            <Card key={index} className={`group hover:shadow-2xl transition-all duration-500 hover:-translate-y-3 border-0 shadow-lg relative overflow-hidden animate-fade-in ${
              service.popular ? 'ring-2 ring-yellow-400/50' : ''
            }`} style={{ animationDelay: `${400 + index * 100}ms` }}>
              {/* Popular Badge */}
              {service.popular && (
                <div className="absolute -top-2 -right-2 z-20">
                  <div className="bg-gradient-to-r from-yellow-400 to-orange-400 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg">
                    {t('popular')}
                  </div>
                </div>
              )}

              {/* Background Gradient - removed blur effect */}
              <div className={`absolute inset-0 bg-gradient-to-br from-${service.color}-50/50 to-${service.color}-100/50 opacity-0 group-hover:opacity-100 transition-opacity duration-500`}></div>
              
              {/* Removed Floating Particles with blur effects */}

              <CardContent className="p-6 text-center relative z-10">
                <div className={`bg-gradient-to-br from-${service.color}-100 to-${service.color}-200 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4 group-hover:scale-110 group-hover:rotate-6 transition-all duration-300 relative`}>
                  <service.icon className={`h-8 w-8 text-${service.color}-600 group-hover:scale-110 transition-transform duration-300`} />
                  {/* Removed blur effect div here */}
                </div>
                
                <h3 className="text-xl font-bold text-gray-800 mb-2 group-hover:scale-105 transition-transform duration-300">{service.title}</h3>
                <p className="text-gray-600 mb-4 text-sm leading-relaxed">{service.description}</p>
                
                <div className={`bg-gradient-to-r from-${service.color}-50 to-${service.color}-100 rounded-lg py-3 px-4 group-hover:scale-105 transition-transform duration-300 border border-${service.color}-200/50`}>
                  <span className={`text-${service.color}-700 font-semibold`}>{service.price}</span>
                </div>

                {/* Removed Hover Overlay with blur */}
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center mt-16 animate-fade-in delay-1000">
          <div className="bg-gradient-to-r from-blue-600/10 via-purple-600/10 to-blue-600/10 backdrop-blur-md rounded-2xl p-8 max-w-2xl mx-auto shadow-xl border border-white/20 relative overflow-hidden group">
            {/* Background Animation */}
            <div className="absolute inset-0 bg-gradient-to-r from-blue-600/5 to-purple-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            
            {/* Floating Elements */}
            <div className="absolute -top-4 -right-4 w-8 h-8 bg-gradient-to-br from-yellow-400/30 to-orange-400/30 rounded-full blur-lg animate-pulse"></div>
            <div className="absolute -bottom-4 -left-4 w-6 h-6 bg-gradient-to-br from-green-400/30 to-blue-400/30 rounded-full blur-lg animate-pulse delay-1000"></div>
            
            <div className="relative z-10">
              <h3 className="text-2xl font-bold mb-4 group-hover:scale-105 transition-transform duration-300">
                <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  {t('specialOffer')}
                </span>
              </h3>
              <p className="text-blue-700 leading-relaxed">
                {t('specialOfferDesc')}
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Services;
