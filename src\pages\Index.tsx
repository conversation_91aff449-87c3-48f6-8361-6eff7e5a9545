
import { <PERSON>, MapPin, File<PERSON>ext, Printer, <PERSON>, Clock, <PERSON>rk<PERSON>, Zap, Star } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import Services from "@/components/Services";
import About from "@/components/About";
import Contact from "@/components/Contact";
import WhatsAppButton from "@/components/WhatsAppButton";
import LanguageSelector from "@/components/LanguageSelector";
import { useLanguage } from "@/contexts/LanguageContext";

const Index = () => {
  const { t } = useLanguage();
  
  const scrollToSection = (id: string) => {
    const element = document.getElementById(id);
    element?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-green-400/20 to-blue-400/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br from-purple-400/10 to-pink-400/10 rounded-full blur-3xl animate-pulse delay-500"></div>
      </div>

      {/* Header/Navigation with Glassmorphism */}
      <header className="bg-white/80 backdrop-blur-md shadow-lg sticky top-0 z-50 border-b border-white/20">
        <div className="container mx-auto px-4 py-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-2 group">
              <div className="relative">
                <Printer className="h-8 w-8 text-blue-600 group-hover:scale-110 transition-transform duration-300" />
                <div className="absolute inset-0 bg-blue-600/20 rounded-full blur-lg group-hover:blur-xl transition-all duration-300"></div>
              </div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                GPCS World
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <nav className="hidden md:flex space-x-6">
                {['home', 'services', 'about', 'contact'].map((section, index) => (
                  <button 
                    key={section}
                    onClick={() => scrollToSection(section)} 
                    className="relative text-gray-600 hover:text-blue-600 transition-all duration-300 px-3 py-2 rounded-lg hover:bg-white/50 backdrop-blur-sm group"
                    style={{ animationDelay: `${index * 100}ms` }}
                  >
                    <span className="relative z-10 capitalize">{t(section)}</span>
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-lg scale-0 group-hover:scale-100 transition-transform duration-300"></div>
                  </button>
                ))}
              </nav>
              <LanguageSelector />
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section with Enhanced Animations */}
      <section id="home" className="py-20 px-4 relative">
        <div className="container mx-auto text-center">
          <div className="max-w-4xl mx-auto">
            {/* Floating Badge */}
            <div className="inline-flex items-center gap-2 bg-white/80 backdrop-blur-md rounded-full px-6 py-3 mb-8 shadow-lg border border-white/20 animate-fade-in">
              <Sparkles className="h-5 w-5 text-yellow-500 animate-spin" />
              <span className="text-sm font-medium bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                ✨ Best Cyber Services in Town ✨
              </span>
            </div>

            <h1 className="text-5xl md:text-7xl font-bold text-gray-800 mb-6 leading-tight animate-fade-in">
              <span className="relative inline-block">
                <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-blue-600 bg-clip-text text-transparent animate-gradient bg-300% bg-[length:300%_300%]">
                  {t('heroTitle')}
                </span>
                <div className="absolute -inset-1 bg-gradient-to-r from-blue-600/20 to-purple-600/20 blur-lg -z-10 animate-pulse"></div>
              </span>
            </h1>

            <p className="text-xl md:text-2xl text-gray-600 mb-4 animate-fade-in delay-200">
              {t('heroSubtitle')}
            </p>
            <p className="text-lg text-gray-600 mb-8 max-w-3xl mx-auto animate-fade-in delay-300">
              {t('heroDescription')}
            </p>
            
            {/* Enhanced Stats with Floating Animation */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-2xl mx-auto">
              {[
                { icon: Users, count: "500+", label: t('happyCustomers'), color: "blue", delay: "0" },
                { icon: FileText, count: "1000+", label: t('formsFilled'), color: "green", delay: "200" },
                { icon: Clock, count: "5+ Years", label: t('experience'), color: "purple", delay: "400" }
              ].map((stat, index) => (
                <div key={index} className="text-center group animate-fade-in" style={{ animationDelay: `${600 + parseInt(stat.delay)}ms` }}>
                  <div className="bg-white/80 backdrop-blur-md rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-500 hover:-translate-y-2 border border-white/20 relative overflow-hidden group">
                    <div className="absolute inset-0 bg-gradient-to-br from-white/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    <div className={`relative mb-4 inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-br from-${stat.color}-100 to-${stat.color}-200 group-hover:scale-110 transition-transform duration-300`}>
                      <stat.icon className={`h-8 w-8 text-${stat.color}-600`} />
                      <div className={`absolute inset-0 bg-${stat.color}-400/20 rounded-full blur-lg group-hover:blur-xl transition-all duration-300`}></div>
                    </div>
                    <h3 className="text-2xl font-bold text-gray-800 mb-2 group-hover:scale-105 transition-transform duration-300">{stat.count}</h3>
                    <p className="text-gray-600">{stat.label}</p>
                    
                    {/* Floating particles */}
                    <div className="absolute top-2 right-2 w-2 h-2 bg-gradient-to-r from-yellow-400 to-orange-400 rounded-full opacity-0 group-hover:opacity-100 animate-ping delay-300"></div>
                    <div className="absolute bottom-2 left-2 w-1.5 h-1.5 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full opacity-0 group-hover:opacity-100 animate-ping delay-500"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <Services />

      {/* About Section */}
      <About />

      {/* Contact Section */}
      <Contact />

      {/* Enhanced Footer */}
      <footer className="bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 text-white py-12 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 to-purple-600/10"></div>
        <div className="container mx-auto px-4 text-center relative z-10">
          <div className="flex items-center justify-center space-x-2 mb-6 group">
            <div className="relative">
              <Printer className="h-8 w-8 group-hover:scale-110 transition-transform duration-300" />
              <div className="absolute inset-0 bg-white/20 rounded-full blur-lg group-hover:blur-xl transition-all duration-300"></div>
            </div>
            <span className="text-2xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
              GPCS World
            </span>
          </div>
          <p className="text-gray-300 mb-6 text-lg">{t('footerTagline')}</p>
          
          {/* Social proof badges */}
          <div className="flex justify-center items-center gap-6 mb-6">
            <div className="flex items-center gap-2 bg-white/10 backdrop-blur-md rounded-full px-4 py-2">
              <Star className="h-5 w-5 text-yellow-400 fill-current" />
              <span className="text-sm">4.9/5 Rating</span>
            </div>
            <div className="flex items-center gap-2 bg-white/10 backdrop-blur-md rounded-full px-4 py-2">
              <Zap className="h-5 w-5 text-green-400" />
              <span className="text-sm">Fast Service</span>
            </div>
          </div>
          
          <p className="text-sm text-gray-400">© 2024 Graphic and Cyber Support. All rights reserved.</p>
        </div>
      </footer>

      {/* WhatsApp Button */}
      <WhatsAppButton />
    </div>
  );
};

export default Index;
