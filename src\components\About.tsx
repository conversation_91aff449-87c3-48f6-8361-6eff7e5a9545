
import { Award, Clock, Users, Star } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { useLanguage } from "@/contexts/LanguageContext";

const About = () => {
  const { t } = useLanguage();
  
  return (
    <section id="about" className="py-20 px-4 bg-gradient-to-br from-blue-50 to-green-50">
      <div className="container mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-4">
            {t('aboutTitle')}
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            {t('aboutSubtitle')}
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Story Section */}
          <div>
            <div className="bg-white rounded-lg p-8 shadow-lg">
              <h3 className="text-2xl font-bold text-gray-800 mb-6">{t('ourStory')}</h3>
              <p className="text-gray-600 mb-4 leading-relaxed">
                {t('aboutDescription1')}
              </p>
              <p className="text-gray-600 mb-4 leading-relaxed">
                {t('aboutDescription2')}
              </p>
              <p className="text-gray-600 leading-relaxed">
                {t('aboutDescription3')}
              </p>
            </div>
          </div>

          {/* Features Grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
            <Card className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6 text-center">
                <Award className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                <h4 className="text-lg font-bold text-gray-800 mb-2">{t('qualityService')}</h4>
                <p className="text-gray-600 text-sm">{t('qualityServiceDesc')}</p>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6 text-center">
                <Clock className="h-12 w-12 text-green-600 mx-auto mb-4" />
                <h4 className="text-lg font-bold text-gray-800 mb-2">{t('fastService')}</h4>
                <p className="text-gray-600 text-sm">{t('fastServiceDesc')}</p>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6 text-center">
                <Users className="h-12 w-12 text-purple-600 mx-auto mb-4" />
                <h4 className="text-lg font-bold text-gray-800 mb-2">{t('expertTeam')}</h4>
                <p className="text-gray-600 text-sm">{t('expertTeamDesc')}</p>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6 text-center">
                <Star className="h-12 w-12 text-yellow-600 mx-auto mb-4" />
                <h4 className="text-lg font-bold text-gray-800 mb-2">{t('customerSatisfaction')}</h4>
                <p className="text-gray-600 text-sm">{t('customerSatisfactionDesc')}</p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Testimonial */}
        <div className="mt-16">
          <div className="bg-white rounded-lg p-8 shadow-lg max-w-4xl mx-auto">
            <div className="text-center mb-6">
              <div className="flex justify-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="h-6 w-6 text-yellow-400 fill-current" />
                ))}
              </div>
              <blockquote className="text-xl text-gray-700 italic mb-4">
                {t('testimonialText')}
              </blockquote>
              <cite className="text-gray-600 font-semibold">{t('testimonialAuthor')}</cite>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
