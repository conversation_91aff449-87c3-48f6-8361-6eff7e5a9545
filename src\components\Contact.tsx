
import { MapPin, Phone, Clock, Mail, Navigation } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { useLanguage } from "@/contexts/LanguageContext";

const Contact = () => {
  const { t } = useLanguage();
  
  return (
    <section id="contact" className="py-20 px-4 bg-white">
      <div className="container mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-4">
            {t('contactTitle')}
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            {t('contactSubtitle')}
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Contact Information */}
          <div className="space-y-6">
            <Card className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start space-x-4">
                  <MapPin className="h-8 w-8 text-blue-600 mt-1" />
                  <div>
                    <h3 className="text-xl font-bold text-gray-800 mb-2">{t('ourAddress')}</h3>
                    <p className="text-gray-600 leading-relaxed">
                      {t('addressLine1')}<br />
                      {t('addressLine2')}<br />
                      {t('addressLine3')}<br />
                      <span className="text-sm text-gray-500">{t('addressLandmark')}</span>
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start space-x-4">
                  <Phone className="h-8 w-8 text-green-600 mt-1" />
                  <div>
                    <h3 className="text-xl font-bold text-gray-800 mb-2">{t('phoneWhatsapp')}</h3>
                    <p className="text-gray-600 mb-2">
                      <strong>{t('callLabel')}</strong> +91 98765 43210<br />
                      <strong>{t('whatsappLabel')}</strong> +91 98765 43210
                    </p>
                    <Button className="bg-green-600 hover:bg-green-700 text-white">
                      {t('whatsappNow')}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start space-x-4">
                  <Clock className="h-8 w-8 text-purple-600 mt-1" />
                  <div>
                    <h3 className="text-xl font-bold text-gray-800 mb-2">{t('shopTimings')}</h3>
                    <div className="text-gray-600">
                      <p><strong>{t('weekdays')}</strong> {t('weekdayHours')}</p>
                      <p><strong>{t('sunday')}</strong> {t('sundayHours')}</p>
                      <p className="text-sm text-green-600 mt-2">{t('emergencyServices')}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start space-x-4">
                  <Mail className="h-8 w-8 text-blue-600 mt-1" />
                  <div>
                    <h3 className="text-xl font-bold text-gray-800 mb-2">{t('emailUs')}</h3>
                    <p className="text-gray-600">
                      <EMAIL><br />
                      <span className="text-sm text-gray-500">{t('emailResponse')}</span>
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Map and Quick Actions */}
          <div className="space-y-6">
            {/* Map Placeholder */}
            <Card className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center">
                  <Navigation className="h-6 w-6 text-blue-600 mr-2" />
                  {t('findUsOnMap')}
                </h3>
                <div className="bg-gray-100 h-64 rounded-lg flex items-center justify-center">
                  <div className="text-center text-gray-600">
                    <MapPin className="h-12 w-12 mx-auto mb-4 text-blue-600" />
                    <p className="font-semibold">{t('interactiveMap')}</p>
                    <p className="text-sm">{t('clickDirections')}</p>
                  </div>
                </div>
                <Button className="w-full mt-4 bg-blue-600 hover:bg-blue-700">
                  {t('openInGoogleMaps')}
                </Button>
              </CardContent>
            </Card>

            {/* Quick Contact Form */}
            <Card className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <h3 className="text-xl font-bold text-gray-800 mb-4">{t('quickEnquiry')}</h3>
                <div className="space-y-4">
                  <input 
                    type="text" 
                    placeholder={t('yourName')}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <input 
                    type="tel" 
                    placeholder={t('phoneNumber')}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <textarea 
                    placeholder={t('whatServiceNeed')}
                    rows={3}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  ></textarea>
                  <Button className="w-full bg-green-600 hover:bg-green-700">
                    {t('sendEnquiry')}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Important Notice */}
        <div className="mt-12 text-center">
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 max-w-2xl mx-auto">
            <h4 className="text-lg font-bold text-yellow-800 mb-2">{t('importantNotice')}</h4>
            <p className="text-yellow-700">
              {t('covidSafety')}<br />
              {t('digitalPayment')}
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Contact;
